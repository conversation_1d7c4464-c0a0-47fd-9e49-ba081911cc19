{"模拟光敏电阻_Interface": {"functions": [{"name": "photoresistor_init", "description": "Initializes the analog photoresistor sensor on GPIO34.", "return_type": "void", "parameters": []}, {"name": "photoresistor_read_raw", "description": "Reads the raw 12-bit ADC value from the photoresistor.", "return_type": "uint16_t", "parameters": []}, {"name": "photoresistor_read_lux", "description": "Converts the raw ADC value to approximate illuminance in lux.", "return_type": "float", "parameters": []}, {"name": "photoresistor_set_dark_threshold", "description": "Sets the raw ADC threshold below which the environment is considered dark.", "return_type": "void", "parameters": [{"name": "threshold", "type": "uint16_t"}]}, {"name": "photoresistor_is_dark", "description": "Returns true if the current light level is below the configured dark threshold.", "return_type": "bool", "parameters": []}]}}