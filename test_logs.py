#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试日志系统的脚本
"""

import os
import sys
import time
import requests
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_log_api():
    """测试日志API"""
    base_url = "http://127.0.0.1:5000"
    
    # 首先获取一个有效的token（如果需要的话）
    # 这里假设我们有一个测试工作流ID
    test_workflow_id = "test-workflow-123"
    
    try:
        # 测试获取工作流状态
        response = requests.get(f"{base_url}/api/workflows/{test_workflow_id}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if 'logs' in data:
                print(f"日志长度: {len(data['logs'])} 字符")
                print(f"日志内容: {data['logs'][:200]}...")  # 只显示前200字符
            else:
                print("响应中没有logs字段")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保Flask应用正在运行")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

def test_direct_log():
    """直接测试日志函数"""
    try:
        # 导入Flask应用上下文
        from app import create_app
        from app.services.workflow_service import _log_with_personality, _log
        
        app = create_app('development')
        
        with app.app_context():
            test_workflow_id = "test-direct-123"
            
            print("测试直接日志写入...")
            _log(test_workflow_id, "这是一个测试日志")
            _log_with_personality(test_workflow_id, "让我先分析一下用户的需求...", "thinking")
            _log_with_personality(test_workflow_id, "现在我来开发 test_driver.h 文件...", "coding")
            _log_with_personality(test_workflow_id, "正在编译固件...", "compiling")
            _log_with_personality(test_workflow_id, "编译成功！", "success")
            
            print("日志写入完成，检查数据库...")
            
            # 检查数据库中的日志
            from app.models import WorkflowState
            record = WorkflowState.query.get(test_workflow_id)
            if record:
                print(f"数据库中的日志长度: {len(record.logs)} 字符")
                print(f"日志内容:\n{record.logs}")
            else:
                print("数据库中没有找到记录")
                
    except Exception as e:
        print(f"直接测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== 测试日志系统 ===\n")
    
    print("1. 测试直接日志写入:")
    test_direct_log()
    
    print("\n2. 测试API响应:")
    test_log_api()
    
    print("\n=== 测试完成 ===")
