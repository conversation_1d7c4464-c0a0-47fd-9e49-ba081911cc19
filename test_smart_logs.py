#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试智能日志系统的示例脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.workflow_service import _log_with_personality, analyze_compilation_error

def test_smart_logging():
    """测试智能日志功能"""
    print("=== 测试智能日志系统 ===\n")
    
    # 模拟工作流ID
    workflow_id = "test-workflow-123"
    
    # 测试不同类型的日志
    print("1. 测试思考日志:")
    _log_with_personality(workflow_id, "让我先分析一下用户的需求...用户想要一个智能报警器", "thinking")
    
    print("\n2. 测试规划日志:")
    _log_with_personality(workflow_id, "现在我来设计整体架构...需要超声波传感器和蜂鸣器模块", "planning")
    
    print("\n3. 测试编码日志:")
    _log_with_personality(workflow_id, "现在我来开发 hcsr04_driver.h 文件...这是超声波传感器的驱动头文件", "coding")
    
    print("\n4. 测试编译日志:")
    _log_with_personality(workflow_id, "正在编译智能报警器的固件...检查代码语法和依赖关系", "compiling")
    
    print("\n5. 测试成功日志:")
    _log_with_personality(workflow_id, "太好了！编译成功，固件已准备就绪", "success")

def test_error_analysis():
    """测试错误分析功能"""
    print("\n=== 测试错误分析系统 ===\n")
    
    # 测试未声明标识符错误
    error_log1 = """
    /path/to/src/hcsr04_driver.cpp:42:5: error: 'digitalRead' was not declared in this scope
         if (digitalRead(ECHO_PIN) == HIGH) {
             ^~~~~~~~~~~
    """
    
    analysis1 = analyze_compilation_error(error_log1)
    print("1. 未声明标识符错误分析:")
    print(f"   文件: {analysis1['file_location']}")
    print(f"   行号: {analysis1['line_number']}")
    print(f"   错误类型: {analysis1['error_type']}")
    print(f"   AI思考: {analysis1['ai_thought']}")
    
    # 测试缺少头文件错误
    error_log2 = """
    /path/to/src/app_main.ino:7:10: fatal error: Arduino.h: No such file or directory
     #include <Arduino.h>
              ^~~~~~~~~~~
    compilation terminated.
    """
    
    analysis2 = analyze_compilation_error(error_log2)
    print("\n2. 缺少头文件错误分析:")
    print(f"   文件: {analysis2['file_location']}")
    print(f"   行号: {analysis2['line_number']}")
    print(f"   错误类型: {analysis2['error_type']}")
    print(f"   AI思考: {analysis2['ai_thought']}")
    
    # 测试语法错误
    error_log3 = """
    /path/to/src/buzzer_driver.cpp:15:1: error: expected ';' before 'void'
     void buzzer_init() {
     ^~~~
    """
    
    analysis3 = analyze_compilation_error(error_log3)
    print("\n3. 语法错误分析:")
    print(f"   文件: {analysis3['file_location']}")
    print(f"   行号: {analysis3['line_number']}")
    print(f"   错误类型: {analysis3['error_type']}")
    print(f"   AI思考: {analysis3['ai_thought']}")

def simulate_development_workflow():
    """模拟完整的开发工作流程"""
    print("\n=== 模拟智能开发工作流程 ===\n")
    
    workflow_id = "demo-workflow-456"
    
    # 1. 需求分析
    _log_with_personality(workflow_id, "让我先分析一下您的需求...您想要一个智能报警器，需要超声波传感器检测距离", "thinking", delay=1)
    
    # 2. 架构设计
    _log_with_personality(workflow_id, "现在我来设计整体架构...我需要为这个设备规划几个核心模块", "planning", delay=1)
    
    # 3. 开发驱动文件
    _log_with_personality(workflow_id, "现在我来开发 hcsr04_driver.h 文件...这是超声波传感器的驱动头文件", "coding", delay=1)
    _log_with_personality(workflow_id, "接下来编写 hcsr04_driver.cpp 实现文件...实现具体的距离测量逻辑", "coding", delay=1)
    
    # 4. 开发蜂鸣器驱动
    _log_with_personality(workflow_id, "现在开发 active_buzzer_driver.h...蜂鸣器驱动需要支持不同的报警模式", "coding", delay=1)
    _log_with_personality(workflow_id, "编写 active_buzzer_driver.cpp...实现蜂鸣器的控制逻辑", "coding", delay=1)
    
    # 5. 开发主程序
    _log_with_personality(workflow_id, "现在开发主程序 app_main.ino...整合所有模块，实现完整的报警逻辑", "coding", delay=1)
    
    # 6. 编译
    _log_with_personality(workflow_id, "正在编译固件...检查代码语法和依赖关系", "compiling", delay=1)
    
    # 7. 模拟编译错误
    _log_with_personality(workflow_id, "编译失败了！让我看看哪里出了问题...", "error", delay=1)
    _log_with_personality(workflow_id, "我发现错误出现在 hcsr04_driver.cpp 第42行：'digitalRead' was not declared in this scope", "analyzing", delay=1)
    _log_with_personality(workflow_id, "啊，我明白了！我忘记包含 Arduino.h 头文件了", "thinking", delay=1)
    
    # 8. 修复
    _log_with_personality(workflow_id, "现在我来修复 hcsr04_driver.cpp...添加缺失的头文件包含", "fixing", delay=1)
    
    # 9. 重新编译
    _log_with_personality(workflow_id, "再次编译...希望这次能成功", "compiling", delay=1)
    _log_with_personality(workflow_id, "太好了！修复成功，编译通过了！", "success", delay=1)

if __name__ == "__main__":
    try:
        test_smart_logging()
        test_error_analysis()
        simulate_development_workflow()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
