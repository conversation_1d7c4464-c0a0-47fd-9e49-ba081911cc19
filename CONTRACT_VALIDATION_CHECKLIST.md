# 🔧 MQTT通信契约验证清单

## 📋 问题总结
本次错误暴露了工作流中的关键问题：
1. **MQTT主题格式不一致** - 前导斜杠 `/` 的使用不统一
2. **JSON字段名不匹配** - 数据点契约与实际实现不符
3. **缺乏端到端集成测试** - 只测试单设备，未验证设备间通信
4. **契约执行不够严格** - 验证机制存在漏洞

## ✅ 强制验证清单

### 1. MQTT主题契约验证
- [ ] **主题格式统一性**
  - 所有主题必须以 `/` 开头
  - 格式：`/project_name/device_role/data`
  - 发布者和订阅者使用完全相同的主题字符串

- [ ] **主题匹配验证**
  - 订阅代码：`client.subscribe("/exact/topic/string")`
  - 回调比较：`if (topic == "/exact/topic/string")`
  - 发布代码：`client.publish("/exact/topic/string", payload)`

### 2. JSON数据契约验证
- [ ] **字段名一致性**
  - 发布者使用的JSON字段名
  - 订阅者期望的JSON字段名
  - 必须完全匹配（区分大小写）

- [ ] **数据类型一致性**
  - 数值类型：int, float, double
  - 字符串类型：string
  - 布尔类型：boolean

### 3. 代码生成验证
- [ ] **静态代码扫描**
  - 检查是否包含正确的主题字符串
  - 检查是否使用正确的JSON字段名
  - 检查是否有硬编码的占位符主题

- [ ] **契约合规性检查**
  - 验证订阅主题与契约一致
  - 验证发布主题与契约一致
  - 验证JSON字段与DP契约一致

### 4. 集成测试验证
- [ ] **端到端通信测试**
  - 启动所有相关设备
  - 验证消息能够正确传递
  - 验证消息格式正确解析

- [ ] **实时监控测试**
  - 使用MQTT监听器监控所有主题
  - 验证实际发布的主题和数据格式
  - 对比契约与实际实现

## 🛠️ 改进措施

### 1. 代码生成改进
```python
# 强制统一主题格式
topic = f"/{safe_project_name}/{safe_source_role}/data"

# 强制验证主题使用
def validate_topic_usage(code, expected_topics):
    for topic in expected_topics:
        if f'subscribe("{topic}")' not in code:
            raise ContractViolation(f"Missing subscription to {topic}")
        if f'== "{topic}"' not in code:
            raise ContractViolation(f"Missing topic comparison for {topic}")
```

### 2. 契约验证增强
```python
def validate_json_contract(code, dp_contract):
    for dp in dp_contract:
        field_name = dp.get('code')
        if f'doc["{field_name}"]' not in code:
            raise ContractViolation(f"Missing JSON field: {field_name}")
```

### 3. 集成测试自动化
- 自动生成跨设备测试脚本
- 自动验证MQTT消息流
- 自动检查契约合规性

### 4. 实时监控工具
- MQTT全主题监听器
- 契约违反实时检测
- 自动化问题报告

## 🚨 关键检查点

### 开发阶段
1. **代码生成后** - 立即进行静态契约验证
2. **编译前** - 检查所有硬编码字符串
3. **部署前** - 运行集成测试脚本

### 测试阶段
1. **单设备测试** - 验证基本功能
2. **跨设备测试** - 验证通信契约
3. **长期运行测试** - 验证稳定性

### 部署阶段
1. **实时监控** - 监控MQTT消息流
2. **契约审计** - 定期检查契约合规性
3. **问题追踪** - 记录和分析契约违反

## 📊 成功指标

### 技术指标
- [ ] 零契约违反错误
- [ ] 100%主题匹配率
- [ ] 100%JSON字段匹配率
- [ ] 端到端测试通过率 > 95%

### 流程指标
- [ ] 自动化验证覆盖率 > 90%
- [ ] 问题发现时间 < 5分钟
- [ ] 问题修复时间 < 30分钟

## 🔄 持续改进

### 每次发布后
1. 分析契约违反原因
2. 更新验证规则
3. 改进自动化工具
4. 更新文档和清单

### 定期审查
1. 月度契约审计
2. 季度流程优化
3. 年度工具升级

---

**记住：契约是设备间通信的唯一真相来源，任何偏离都会导致系统故障！**
